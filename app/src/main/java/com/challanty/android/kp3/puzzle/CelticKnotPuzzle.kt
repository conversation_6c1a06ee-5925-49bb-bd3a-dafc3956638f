package com.challanty.android.kp3.puzzle

import com.challanty.android.kp3.puzzle.original.OriginalPuzzleGenerator
import com.challanty.android.kp3.puzzle.original.OriginalPuzzleSolver
import kotlin.collections.isNotEmpty

class CelticKnotPuzzle(
    val scrambled: Array<IntArray> = Array(0) { IntArray(0) },
    val solution: Array<IntArray> = Array(0) { IntArray(0) },
    val tileRows: Int = 0,
    val tileCols: Int = 0,

    /**
     * Seed used to generate the puzzle, if any.
     * Can be used to recreate the same puzzle.
     */
    val seed: Long? = null
) {
    private val puzzleGenerator = OriginalPuzzleGenerator()
    private val puzzleSolver = OriginalPuzzleSolver()

    fun isSolved: Boolean {
        return puzzleSolver.isPuzzleSolved(this)
    }

    fun generate(
        rows: Int,
        cols: Int,
        tileRows: Int,
        tileCols: Int,
        isFlipOU: Boolean = false,
        doRotations: Boolean = false,
        seed: Long? = null,
    ): CelticKnotPuzzle {
        return puzzleGenerator.generate(
            rows = rows,
            cols = cols,
            tileRows = tileRows,
            tileCols = tileCols,
            isFlipOU = isFlipOU,
            doRotations = doRotations,
            seed = seed,
        )
    }

    fun scramble(
        solution: Array<IntArray>,
        tileRows: Int,
        tileCols: Int,
        doRotations: Boolean,
        seed: Long?,
    ): Array<IntArray> {
        return puzzleGenerator.scramble(
            solution = solution,
            tileRows = tileRows,
            tileCols = tileCols,
            doRotations = doRotations,
            seed = seed,
        )
    }

    fun swapTiles(
        boardRow1: Int,
        boardCol1: Int,
        boardRow2: Int,
        boardCol2: Int,
    ) {
        puzzleGenerator.swapTiles(
            puzzle = this,
            boardRow1 = boardRow1,
            boardCol1 = boardCol1,
            boardRow2 = boardRow2,
            boardCol2 = boardCol2,
        )
    }

    fun rotateTile(
        boardRow: Int,
        boardCol: Int,
    ) {
        puzzleGenerator.rotateTile(
            puzzle = this,
            boardRow = boardRow,
            boardCol = boardCol,
        )
    }

    fun getScrambledPicUnitIDAt(row: Int, col: Int): Int {
        return scrambled[row][col]
    }

    fun getSolutionPicUnitIDAt(row: Int, col: Int): Int {
        return solution[row][col]
    }

    val rows: Int
        get() = scrambled.size

    val cols: Int
        get() = if (scrambled.isNotEmpty()) scrambled[0].size else 0

    val boardRows: Int
        get() = if (tileRows == 0) 0 else rows / tileRows

    val boardCols: Int
        get() = if (tileCols == 0) 0 else cols / tileCols

}
