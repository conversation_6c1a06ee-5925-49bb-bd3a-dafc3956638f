package com.challanty.android.kp3.viewModel

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.PaintingStyle
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.unit.IntSize
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.challanty.android.kp3.data.Settings
import com.challanty.android.kp3.data.repository.Repository
import com.challanty.android.kp3.puzzle.CelticKnotPuzzle
import com.challanty.android.kp3.puzzle.PuzzleService
import com.challanty.android.kp3.state.GameState
import com.challanty.android.kp3.state.ProcessingStateManager
import com.challanty.android.kp3.state.ProcessingType
import com.challanty.android.kp3.util.Constants
import com.challanty.android.kp3.util.byteString2TwoDIntArray
import com.challanty.android.kp3.util.twoDintArray2ByteString
import com.challanty.android.kp3.viewModel.helper.GameModelHelper
import com.challanty.android.kp3.viewModel.helper.GameUIHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.math.min
import kotlin.random.Random

data class PicUnitDependencies(
    var picUnitRows: Int = 0,
    var picUnitCols: Int = 0,
    var picUnitSize: Float = 0f,
    var picUnitScale: Float = 0f,
    var outlinePaint: Paint = Paint()
)

@HiltViewModel
class GameViewModel @Inject constructor(
    private val repository: Repository,
    private val puzzleService: PuzzleService,
    private val processingStateManager: ProcessingStateManager,
    private val gameUIHelper: GameUIHelper,
    val gameModelHelper: GameModelHelper,
) : ViewModel() {

    // Wait for saved dataStore message to be initialized and then collect
    // updates to settings and alert the viewModel of changes.
    init {
        viewModelScope.launch {
            // Wait for saved dataStore message to be initialized
            // Note: .first cancels its flow subscription when the condition is met.
            repository.savedDataStoreStateFlow.first { saved ->
                !saved.board.isEmpty
            }

            // App is now initialized
            processingStateManager.endProcessing(ProcessingType.APP)

            // Look for changes to the settings dataStore message.
            // Ignore uninitialized settings.
            repository.settingsDataStoreStateFlow.collect { settings ->
                if (settings.version != 0) {
                    onGameSettingsChanged(settings)
                }
            }
        }
    }

    // Track selected tiles so we know when to do a tile swap
    private val selections = mutableSetOf<TileModel>()

    // Things that change with pic unit size
    private val picUnitDependencies = PicUnitDependencies()

    // Used to determine if new settings require a new game
    private var oldSettings = Settings.getDefaultInstance()

    // First game requires special handling.
    private var isFirstGame = true

    // Used to determine if we need to respond to game area size changes
    private var oldGameAreaSize = IntSize.Zero

    // Access to the saved puzzle
    private val savedDataStoreStateFlow = repository.savedDataStoreStateFlow

    private var puzzle = CelticKnotPuzzle()

    // Expose the game state for use in the UI
    fun getGameState(): StateFlow<GameState> = gameUIHelper.gameState

    fun onGameAreSizeChange(newGameAreaSize: IntSize) {
        // Ignore the zero size returned at startup and any redundant changes.
        if (newGameAreaSize == IntSize.Zero || newGameAreaSize == oldGameAreaSize) {
            return
        }

        oldGameAreaSize = newGameAreaSize

        handleGameSetupChange(newGameAreaSize = newGameAreaSize)
    }

    fun onGameSettingsChanged(newSettings: Settings) {
        val isNewGameDimensions = newSettings.boardRows != oldSettings.boardRows ||
                newSettings.boardCols != oldSettings.boardCols ||
                newSettings.tileRows != oldSettings.tileRows ||
                newSettings.tileCols != oldSettings.tileCols

        val isNewGameRequired = isNewGameDimensions ||
                newSettings.tilesRotatable != oldSettings.tilesRotatable ||
                newSettings.lockPercent != oldSettings.lockPercent

        oldSettings = newSettings

        handleGameSetupChange(
            newSettings = newSettings,
            isNewGameDimensions = isNewGameRequired,
            isNewGame = isNewGameRequired
        )
    }

    private fun handleGameSetupChange(
        newGameAreaSize: IntSize? = null,
        newSettings: Settings? = null,
        isNewGameDimensions: Boolean = false,
        isNewGame: Boolean = false,
    ) {

        val settings = newSettings ?: repository.settingsDataStoreStateFlow.value

        // Are we setup enough to do anything?
        if (oldGameAreaSize == IntSize.Zero && newGameAreaSize == null) {
            return
        }

        // Block input and show progress indicator while handling game setup change.
        // Note: The game setup is so fast that the progress indicator hardly gets started
        processingStateManager.startProcessing(ProcessingType.GAME)
        gameUIHelper.toggleProgress(true)

        viewModelScope.launch {

            if (newGameAreaSize != null || isNewGameDimensions) {
                calcPicUnitDependencies(
                    gamingAreaSize = newGameAreaSize ?: oldGameAreaSize,
                    settings = settings
                )
            }

            if (isNewGame) {
                buildNewPuzzle(settings)
            } else {
                buildExistingPuzzle(settings)
            }

            val newGameState = gameModelHelper.createNewGameState(
                puzzle = puzzle,
                picUnitDependencies = picUnitDependencies,
                settings = settings
            )

            if (isNewGame) {
                gameUIHelper.showNewGame(
                    gameState = newGameState,
                    settings = settings
                )
            } else {
                if (isFirstGame) {
                    isFirstGame = false
                    gameUIHelper.showStartupGame(
                        gameState = newGameState,
                        settings = settings
                    )
                } else {
                    gameUIHelper.showExistingGame(
                        newGameState = newGameState,
                        settings = settings
                    )
                }
            }

            processingStateManager.endProcessing(ProcessingType.GAME)
        }

        return
    }

    fun onNewGameClick() {
        handleGameSetupChange(isNewGame = true)
    }

    fun onTileClick(tile: TileModel) {
        if (processingStateManager.isProcessing()) return

        if (selections.contains(tile)) {
            selections.remove(tile)
        } else {
            selections.add(tile)
        }

        tile.toggleSelection()

        if (selections.size == 2) {
            handleTileSwap(selections.first(), selections.last())
        }
    }

    fun onTileDoubleClick(tile: TileModel) {
        val settings = repository.settingsDataStoreStateFlow.value

        if (processingStateManager.isProcessing() ||
            !settings.tilesRotatable
        ) return

        if (selections.contains(tile)) {
            selections.remove(tile)
            tile.toggleSelection()
        }

        processingStateManager.startProcessing(ProcessingType.ANIMATION)

        viewModelScope.launch {

            gameUIHelper.handleTileRotation(
                tile = tile,
                settings = settings
            )

            puzzleService.rotateTile(
                puzzle = puzzle,
                tile = tile
            )
            repository.setBoard(twoDintArray2ByteString(puzzle.scrambled))

            if (puzzleService.isPuzzleSolved(puzzle)) {
                gameUIHelper.toggleSolvedBoard(true)
            }

            processingStateManager.endProcessing(ProcessingType.ANIMATION)
        }
    }

    private fun handleTileSwap(
        tile1: TileModel,
        tile2: TileModel
    ) {
        // Deselect the two selected tiles and swap their locations on the game board
        processingStateManager.startProcessing(ProcessingType.ANIMATION)

        viewModelScope.launch {

            tile1.toggleSelection()
            tile2.toggleSelection()

            selections.clear()

            gameUIHelper.handleTileSwap(
                tile1 = tile1,
                tile2 = tile2,
                settings = repository.settingsDataStoreStateFlow.value
            )

            puzzleService.swapTiles(
                puzzle = puzzle,
                tile1 = tile1,
                tile2 = tile2,
            )
            repository.setBoard(twoDintArray2ByteString(puzzle.scrambled))

            if (puzzleService.isPuzzleSolved(puzzle)) {
                gameUIHelper.toggleSolvedBoard(true)
            }

            processingStateManager.endProcessing(ProcessingType.ANIMATION)
        }
    }

    private fun calcPicUnitDependencies(
        gamingAreaSize: IntSize,
        settings: Settings
    ) {
        picUnitDependencies.picUnitRows = settings.boardRows * settings.tileRows
        picUnitDependencies.picUnitCols = settings.boardCols * settings.tileCols

        // Max possible picture unit pixel size if it could be rectangular
        val maxPicUnitHeight = gamingAreaSize.height / picUnitDependencies.picUnitRows
        val maxPicUnitWidth = gamingAreaSize.width / picUnitDependencies.picUnitCols

        // Use the smaller width/height size to make the largest possible
        // square picture unit that fits within the gaming area
        picUnitDependencies.picUnitSize = min(maxPicUnitWidth, maxPicUnitHeight).toFloat()

        picUnitDependencies.picUnitScale = picUnitDependencies.picUnitSize / Constants.PIC_UNIT_BOX_SIZE

        picUnitDependencies.outlinePaint = Paint().apply {
            color = Color.Black // TODO: Make this a theme color
            style = PaintingStyle.Stroke
            strokeWidth = .1f * picUnitDependencies.picUnitSize
            strokeCap = StrokeCap.Square
            isAntiAlias = true
        }
    }

    private suspend fun buildNewPuzzle(settings: Settings) {

        puzzle = puzzleService.generatePuzzle(
            rows = picUnitDependencies.picUnitRows,
            cols = picUnitDependencies.picUnitCols,
            tileRows = settings.tileRows,
            tileCols = settings.tileCols,
            isFlipOU = Random.nextBoolean(),
            doRotations = settings.tilesRotatable,
            seed = Random.nextLong(),
        )

        repository.setSolution(twoDintArray2ByteString(puzzle.solution))
        repository.setBoard(twoDintArray2ByteString(puzzle.scrambled))
    }

    private fun buildExistingPuzzle(settings: Settings) {

        val picUnitRows = picUnitDependencies.picUnitRows
        val picUnitCols = picUnitDependencies.picUnitCols
        val savedDataStore = savedDataStoreStateFlow.value

        puzzle = CelticKnotPuzzle(
            scrambled = byteString2TwoDIntArray(
                byteString = savedDataStore.board,
                rows = picUnitRows,
                cols = picUnitCols
            ),
            solution = byteString2TwoDIntArray(
                byteString = savedDataStore.solution,
                rows = picUnitRows,
                cols = picUnitCols
            ),
            tileRows = settings.tileRows,
            tileCols = settings.tileCols
        )
    }
}