package com.challanty.android.kp3.di

import com.challanty.android.kp3.puzzle.CelticKnotPuzzle
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent

@Module
@InstallIn(ViewModelComponent::class)
object PuzzleServiceModule {

    @Provides
    @OriginalCelticKnotPuzzle
    fun provideOriginalCelticKnotPuzzle(): CelticKnotPuzzle {
        return CelticKnotPuzzle()
    }

// You can add more for other types, e.g., @ExpertPuzzle, @DailyChallengePuzzle, etc.

}