package com.challanty.android.kp3.di

import javax.inject.Qualifier

// A qualifier for the ORIGINAL puzzle service
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class OriginalPuzzle

// A qualifier for the ORIGINAL CelticKnotPuzzle
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class OriginalCelticKnotPuzzle

// You can add more for other types, e.g., @ExpertPuzzle, @DailyChallengePuzzle, etc.