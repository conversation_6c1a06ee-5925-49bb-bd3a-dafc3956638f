package com.challanty.android.kp3.puzzle.original

import com.challanty.android.kp3.puzzle.CelticKnotPuzzle
import com.challanty.android.kp3.puzzle.original.PicUnitLinks.Companion.oppSide
import com.challanty.android.kp3.viewModel.TileModel

// links = The type of link for the top, right, bottom, and left edges
// of a picture unit. The links of the sides of proper neighboring pic units always
// add to zero (the sides have no links) or 6 (the sides link properly).
// 0 = no link
// 1 = left corner link going over
// 2 = left corner link going under
// 3 = middle link
// 4 = right corner link going over
// 5 = right corner link going under
//
//      L  M  R   L/M/R = left/middle/right
//      -------
//    R |     | L
//    M |     | M
//    L |     | R
//      -------
//      R  M  L

// The type of link at each side of a picture unit
class PicUnitLinks(
    private val top: Int,
    private val rht: Int,
    private val bot: Int,
    private val lft: Int
) {

    companion object {
        fun oppSide(side: Int): Int {
            return (side + 2) % 4
        }
    }

    private val links = intArrayOf(top, rht, bot, lft)

    fun quarterRotated(rotations: Int): PicUnitLinks {
        return when (rotations % 4) {
            0 -> PicUnitLinks(top, rht, bot, lft)
            1 -> PicUnitLinks(lft, top, rht, bot)
            2 -> PicUnitLinks(bot, lft, top, rht)
            else -> PicUnitLinks(rht, bot, lft, top)
        }
    }

    fun getLink(side: Int): Int {
        return links[side]
    }
}

class OriginalPuzzleSolver {
    val noLink = 0
    val lftOverLink = 1
    val lftUnderLink = 2
    val midLink = 3
    val rhtOverLink = 4
    val rhtUnderLink = 5

    val topSide = 0
    val rhtSide = 1
    val botSide = 2
    val lftSide = 3


    private val borderPicUnitLinks = PicUnitLinks(noLink, noLink, noLink, noLink)
    private val picUnitALinks = PicUnitLinks(lftOverLink, noLink, midLink, rhtOverLink)
    private val picUnitELinks = PicUnitLinks(lftUnderLink, noLink, midLink, rhtUnderLink)
    private val picUnitILinks = PicUnitLinks(rhtOverLink, lftOverLink, midLink, noLink)
    private val picUnitMLinks = PicUnitLinks(rhtUnderLink, lftUnderLink, midLink, noLink)
    private val picUnitQLinks = PicUnitLinks(lftUnderLink, rhtOverLink, lftOverLink, rhtUnderLink)
    private val picUnitULinks = PicUnitLinks(noLink, midLink, midLink, noLink)
    private val picUnitYLinks = PicUnitLinks(midLink, noLink, midLink, noLink)

    private val id2Links = Array(27) { borderPicUnitLinks }

    init {
        id2Links[0] = picUnitALinks
        id2Links[1] = picUnitALinks.quarterRotated(1)
        id2Links[2] = picUnitALinks.quarterRotated(2)
        id2Links[3] = picUnitALinks.quarterRotated(3)
        id2Links[4] = picUnitELinks
        id2Links[5] = picUnitELinks.quarterRotated(1)
        id2Links[6] = picUnitELinks.quarterRotated(2)
        id2Links[7] = picUnitELinks.quarterRotated(3)
        id2Links[8] = picUnitILinks
        id2Links[9] = picUnitILinks.quarterRotated(1)
        id2Links[10] = picUnitILinks.quarterRotated(2)
        id2Links[11] = picUnitILinks.quarterRotated(3)
        id2Links[12] = picUnitMLinks
        id2Links[13] = picUnitMLinks.quarterRotated(1)
        id2Links[14] = picUnitMLinks.quarterRotated(2)
        id2Links[15] = picUnitMLinks.quarterRotated(3)
        id2Links[16] = picUnitQLinks
        id2Links[17] = picUnitQLinks.quarterRotated(1)
        id2Links[18] = picUnitQLinks.quarterRotated(2)
        id2Links[19] = picUnitQLinks.quarterRotated(3)
        id2Links[20] = picUnitULinks
        id2Links[21] = picUnitULinks.quarterRotated(1)
        id2Links[22] = picUnitULinks.quarterRotated(2)
        id2Links[23] = picUnitULinks.quarterRotated(3)
        id2Links[24] = picUnitYLinks
        id2Links[25] = picUnitYLinks.quarterRotated(1)
        id2Links[26] = borderPicUnitLinks
    }

    fun isPuzzleSolved(puzzle: CelticKnotPuzzle): Boolean {
        val picUnitMatrix = puzzle.scrambled
        val picUnitRows = puzzle.rows
        val picUnitCols = puzzle.cols
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols
        val boardRows = puzzle.boardRows
        val boardCols = puzzle.boardCols

        // Check all interior left walls of tiles
        for (boardRow in 0 until boardRows) {
            for (boardCol in 1 until boardCols) {
                for (tileRow in 0 until tileRows) {
                    val startRow = boardRow * tileRows
                    val startCol = boardCol * tileCols
                    val picUnit1 = picUnitMatrix[startRow + tileRow][startCol]
                    val picUnit2 = picUnitMatrix[startRow + tileRow][startCol - 1]

                    if (!areProperNeighbors(picUnit1, picUnit2, lftSide)) {
                        println("Left wall failure at $startRow/$startCol for tile at $boardRow/$boardCol")
                        return false
                    }
                }
            }
        }

        // Check all interior bottom walls of tiles
        for (boardRow in 0 until boardRows - 1) {
            for (boardCol in 0 until boardCols) {
                for (tileCol in 0 until tileCols) {
                    val startRow = (boardRow * tileRows) + tileRows - 1
                    val startCol = boardCol * tileCols
                    val picUnit1 = picUnitMatrix[startRow][startCol + tileCol]
                    val picUnit2 = picUnitMatrix[startRow + 1][startCol + tileCol]

                    if (!areProperNeighbors(picUnit1, picUnit2, botSide)) {
                        println("Bottom wall failure at $startRow/$startCol for tile at $boardRow/$boardCol")
                        return false
                    }
                }
            }
        }

        // Check all exterior left and right walls
        val startCol = 0
        val endCol = picUnitCols - 1
        for (boardRow in 0 until boardRows) {
            for (tileRow in 0 until tileRows) {
                val startRow = boardRow * tileRows
                val picUnit1 = picUnitMatrix[startRow + tileRow][startCol]
                val picUnit2 = picUnitMatrix[startRow + tileRow][endCol]

                if (!areProperNeighbors(picUnit1, 26, lftSide)) {
                    println("Left wall failure at $startRow/$startCol")
                    return false
                }

                if (!areProperNeighbors(picUnit2, 26, rhtSide)) {
                    println("Right wall failure at $startRow/$endCol")
                    return false
                }
            }
        }

        // Check all exterior top and bottom walls
        val startRow = 0
        val endRow = picUnitRows - 1
        for (boardCol in 0 until boardCols) {
            for (tileCol in 0 until tileCols) {
                val startCol = boardCol * tileCols + tileCol
                val picUnit1 = picUnitMatrix[startRow][startCol]
                val picUnit2 = picUnitMatrix[endRow][startCol]

                if (!areProperNeighbors(picUnit1, 26, topSide)) {
                    println("Top wall failure at $startRow/$startCol")
                    return false
                }


                if (!areProperNeighbors(picUnit2, 26, botSide)) {
                    println("Bottom wall failure at $endRow/$startCol")
                    return false
                }
            }
        }

        return true
    }

    fun isTileSolved(
        puzzle: CelticKnotPuzzle,
        tile: TileModel,
    ): Boolean {
        val boardRow = tile.boardPosition.x
        val boardCol = tile.boardPosition.y

        val picUnitMatrix = puzzle.scrambled
        val picUnitRows = puzzle.rows
        val picUnitCols = puzzle.cols
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols
        val boardRows = puzzle.boardRows
        val boardCols = puzzle.boardCols

        // Check left wall
        var startRow = boardRow * tileRows
        var startCol = boardCol * tileCols
                for (tileRow in 0 until tileRows) {
                    val picUnit1 = picUnitMatrix[startRow + tileRow][startCol]
                    val picUnit2 = if (startCol == 0) {
                        26
                    } else {
                        picUnitMatrix[startRow + tileRow][startCol - 1]
                    }

                    if (!areProperNeighbors(picUnit1, picUnit2, lftSide)) {
                        return false
                    }
                }

        // Check bottom wall
        startRow = (boardRow * tileRows) + tileRows - 1
        startCol = boardCol * tileCols
        for (tileCol in 0 until tileCols) {
                    val picUnit1 = picUnitMatrix[startRow][startCol + tileCol]
                    val picUnit2 = if (startRow == picUnitRows - 1) {
                        26
                    } else {
                        picUnitMatrix[startRow + 1][startCol + tileCol]
                    }

                    if (!areProperNeighbors(picUnit1, picUnit2, botSide)) {
                        return false
                    }
                }

        // Check right wall
        startRow = boardRow * tileRows
        startCol = (boardCol * tileCols) + tileCols - 1
        for (tileRow in 0 until tileRows) {
                val picUnit1 = picUnitMatrix[startRow + tileRow][startCol]
                val picUnit2 = if (startCol == picUnitCols - 1) {
                    26
                } else {
                    picUnitMatrix[startRow + tileRow][startCol]
                }

                if (!areProperNeighbors(picUnit1, picUnit2, rhtSide)) {
                    return false
                }
            }

        // Check top wall
        startRow = boardRow * tileRows
        startCol = boardCol * tileCols
            for (tileCol in 0 until tileCols) {
                val picUnit1 = picUnitMatrix[startRow][startCol + tileCol]
                val picUnit2 = if (startRow == 0) {
                    26
                } else {
                    picUnitMatrix[startRow - 1][startCol + tileCol]
                }

                if (!areProperNeighbors(picUnit1, picUnit2, topSide)) {
                    return false
                }
        }

        return true
    }

    // Is the specified side of picUnit1 a proper neighbor for the opposite side of picUnit2?
    private fun areProperNeighbors(
        picUnit1: Int,
        picUnit2: Int,
        side: Int,
    ): Boolean {
        val linkSum = id2Links[picUnit1].getLink(side) + id2Links[picUnit2].getLink(oppSide(side))
        return linkSum == 0 || linkSum == 6
    }
}
