package com.challanty.android.kp3.di

import com.challanty.android.kp3.puzzle.CelticKnotPuzzle
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent

@Module
@InstallIn(ViewModelComponent::class) // Must match the component of the provider
abstract class DefaultBindingsModule {

    @Binds
    abstract fun bindDefaultCelticKnotPuzzle(
        // Tells Hilt to use the @OriginalCelticKnotPuzzle implementation
        // when a CelticKnotPuzzle is requested without a qualifier.
        @OriginalCelticKnotPuzzle originalCelticKnotPuzzleImpl: CelticKnotPuzzle
    ): CelticKnotPuzzle
}