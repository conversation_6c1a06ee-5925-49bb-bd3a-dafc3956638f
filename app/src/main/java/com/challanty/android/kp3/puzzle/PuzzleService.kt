package com.challanty.android.kp3.puzzle

import com.challanty.android.kp3.viewModel.TileModel

/**
 * Interface for puzzle generators.
 * Defines methods to generate and manipulate puzzles for the Celtic knot game.
 */
interface PuzzleService {

    fun generatePuzzle(
        rows: Int,
        cols: Int,
        tileRows: Int,
        tileCols: Int,
        isFlipOU: Boolean = false,
        doRotations: Boolean = false,
        seed: Long? = null,
    ): CelticKnotPuzzle

    fun scrambleSolution(
        solution: Array<IntArray>,
        tileRows: Int,
        tileCols: Int,
        doRotations: Boolean,
        seed: Long?,
    ): Array<IntArray>

    fun swapTiles(
        puzzle: CelticKnotPuzzle,
        tile1: TileModel,
        tile2: TileModel,
    )

    fun rotateTile(
        puzzle: CelticKnotPuzzle,
        tile: TileModel,
    )

    fun isTileSolved(
        puzzle: CelticKnotPuzzle,
        tile: TileModel,
    ): Boolean

    fun isPuzzleSolved(
        puzzle: CelticKnotPuzzle,
    ): <PERSON>olean
}
